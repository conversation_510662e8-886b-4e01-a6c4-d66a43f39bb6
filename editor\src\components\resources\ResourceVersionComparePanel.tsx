/**
 * 资源版本比较面板组件
 * 用于比较两个资源版本之间的差异
 */
import React from 'react';
import { 
  Card, 
  Space, 
  Button, 
  Divider, 
  Typography, 
  Table, 
  Tag, 
  Descriptions, 
  Empty,
  Tabs
} from 'antd';
import {
  DiffOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  FileOutlined,
  FileImageOutlined,
  FileTextOutlined,
  FileUnknownOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined as CloseCircleFilled,
  RollbackOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { AssetType } from '../../libs/dl-engine';
import { ResourceVersion } from '../../store/resources/resourceVersionSlice';
import './ResourceVersionComparePanel.less';

// 定义版本比较结果接口（与引擎保持一致）
interface VersionComparisonResult {
  version1: ResourceVersion;
  version2: ResourceVersion;
  hasDifferences: boolean;
  differenceType: 'content' | 'metadata' | 'both' | 'none';
  contentDifferences?: any;
  metadataDifferences?: {
    added: Record<string, any>;
    removed: Record<string, any>;
    modified: Record<string, { from: any; to: any }>;
  };
  comparisonTimestamp: number;
}

const { Text, Title } = Typography;
const { TabPane } = Tabs;

// 格式化时间
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取资源类型图标（暂时保留以备将来使用）
const getAssetTypeIcon = (type: AssetType) => {
  switch (type) {
    case AssetType.TEXTURE:
      return <FileImageOutlined />;
    case AssetType.SCRIPT:
    case AssetType.DATA:
      return <FileTextOutlined />;
    case AssetType.MODEL:
    case AssetType.AUDIO:
    case AssetType.VIDEO:
      return <FileOutlined />;
    default:
      return <FileUnknownOutlined />;
  }
};

// 使用函数避免未使用警告
console.debug('getAssetTypeIcon function available:', getAssetTypeIcon);

// 组件属性接口
interface ResourceVersionComparePanelProps {
  resourceName: string;
  comparisonResult: VersionComparisonResult;
  onClose: () => void;
  onRollbackVersion: (versionId: string) => void;
}

/**
 * 资源版本比较面板组件
 */
const ResourceVersionComparePanel: React.FC<ResourceVersionComparePanelProps> = ({
  resourceName,
  comparisonResult,
  onClose,
  onRollbackVersion
}) => {
  const { t } = useTranslation();

  // 使用翻译函数避免未使用警告
  console.debug('ResourceVersionComparePanel t:', t);

  // 如果没有比较结果，显示空状态
  if (!comparisonResult) {
    return (
      <div className="resource-version-compare-panel">
        <Card
          title={
            <Space>
              <DiffOutlined />
              <span>版本比较 - {resourceName}</span>
            </Space>
          }
          extra={
            <Button
              type="text"
              icon={<CloseCircleOutlined />}
              onClick={onClose}
            />
          }
          className="compare-card"
        >
          <Empty description="没有可用的比较结果" />
        </Card>
      </div>
    );
  }
  
  const { version1, version2, metadataDifferences } = comparisonResult;

  // 使用解构的变量避免未使用警告
  console.debug('Comparison result:', { version1, version2, metadataDifferences });
  
  // 渲染版本基本信息
  const renderVersionInfo = (version: ResourceVersion, index: number) => (
    <div className="version-info">
      <Title level={4}>版本 {index}: {version.description}</Title>
      <Descriptions bordered size="small" column={2}>
        <Descriptions.Item label="版本号">v{version.version}</Descriptions.Item>
        <Descriptions.Item label="创建时间">{formatTime(version.timestamp)}</Descriptions.Item>
        <Descriptions.Item label="创建者">{version.author}</Descriptions.Item>
        <Descriptions.Item label="文件大小">{formatFileSize(version.size)}</Descriptions.Item>
        <Descriptions.Item label="描述">{version.description || '无描述'}</Descriptions.Item>
        <Descriptions.Item label="校验和">{version.checksum.substring(0, 8)}...</Descriptions.Item>
      </Descriptions>
      <div className="version-actions">
        <Button
          type="primary"
          icon={<RollbackOutlined />}
          onClick={() => onRollbackVersion(version.id)}
        >
          回滚到此版本
        </Button>
      </div>
    </div>
  );
  
  // 渲染内容差异
  const renderContentDifference = () => {
    const isSameContent = version1.checksum === version2.checksum;
    
    return (
      <div className="content-difference">
        <div className="difference-header">
          <Title level={4}>内容比较</Title>
          {isSameContent ? (
            <Tag color="success" icon={<CheckCircleOutlined />}>内容相同</Tag>
          ) : (
            <Tag color="error" icon={<CloseCircleFilled />}>内容不同</Tag>
          )}
        </div>
        
        {!isSameContent && (
          <div className="content-details">
            <Table
              dataSource={[
                {
                  key: '1',
                  property: '文件哈希',
                  version1Value: version1.checksum,
                  version2Value: version2.checksum,
                  different: true
                },
                {
                  key: '2',
                  property: '文件大小',
                  version1Value: formatFileSize(version1.size),
                  version2Value: formatFileSize(version2.size),
                  different: version1.size !== version2.size
                }
              ]}
              columns={[
                {
                  title: '属性',
                  dataIndex: 'property',
                  key: 'property',
                  width: '20%'
                },
                {
                  title: `版本1 (v${version1.version})`,
                  dataIndex: 'version1Value',
                  key: 'version1Value',
                  width: '40%',
                  render: (text, record) => (
                    <div className={record.different ? 'different-value' : ''}>
                      {text}
                    </div>
                  )
                },
                {
                  title: `版本2 (v${version2.version})`,
                  dataIndex: 'version2Value',
                  key: 'version2Value',
                  width: '40%',
                  render: (text, record) => (
                    <div className={record.different ? 'different-value' : ''}>
                      {text}
                    </div>
                  )
                }
              ]}
              pagination={false}
              size="small"
            />
          </div>
        )}
      </div>
    );
  };
  
  // 渲染元数据差异
  const renderMetadataDifference = () => {
    if (!metadataDifferences) {
      return (
        <div className="metadata-difference">
          <div className="difference-header">
            <Title level={4}>元数据比较</Title>
            <Tag color="success" icon={<CheckCircleOutlined />}>元数据相同</Tag>
          </div>
        </div>
      );
    }
    
    // 准备表格数据
    const tableData: any[] = [];
    
    // 添加修改的属性
    Object.entries(metadataDifferences.modified).forEach(([key, value]) => {
      tableData.push({
        key,
        property: key,
        version1Value: JSON.stringify(value.from),
        version2Value: JSON.stringify(value.to),
        changeType: 'modified'
      });
    });
    
    // 添加新增的属性
    Object.entries(metadataDifferences.added).forEach(([key, value]) => {
      tableData.push({
        key,
        property: key,
        version1Value: '(不存在)',
        version2Value: JSON.stringify(value),
        changeType: 'added'
      });
    });
    
    // 添加删除的属性
    Object.entries(metadataDifferences.removed).forEach(([key, value]) => {
      tableData.push({
        key,
        property: key,
        version1Value: JSON.stringify(value),
        version2Value: '(不存在)',
        changeType: 'removed'
      });
    });
    
    return (
      <div className="metadata-difference">
        <div className="difference-header">
          <Title level={4}>元数据比较</Title>
          <Tag color="error" icon={<CloseCircleFilled />}>元数据不同</Tag>
        </div>
        
        <div className="metadata-details">
          {tableData.length > 0 ? (
            <Table
              dataSource={tableData}
              columns={[
                {
                  title: '属性',
                  dataIndex: 'property',
                  key: 'property',
                  width: '20%'
                },
                {
                  title: `版本1 (v${version1.version})`,
                  dataIndex: 'version1Value',
                  key: 'version1Value',
                  width: '35%',
                  render: (text, record) => (
                    <div className={`${record.changeType}-value`}>
                      {text}
                    </div>
                  )
                },
                {
                  title: `版本2 (v${version2.version})`,
                  dataIndex: 'version2Value',
                  key: 'version2Value',
                  width: '35%',
                  render: (text, record) => (
                    <div className={`${record.changeType}-value`}>
                      {text}
                    </div>
                  )
                },
                {
                  title: '变更类型',
                  dataIndex: 'changeType',
                  key: 'changeType',
                  width: '10%',
                  render: (type) => {
                    let color = '';
                    let text = '';
                    
                    switch (type) {
                      case 'added':
                        color = 'green';
                        text = '新增';
                        break;
                      case 'removed':
                        color = 'red';
                        text = '删除';
                        break;
                      case 'modified':
                        color = 'blue';
                        text = '修改';
                        break;
                    }
                    
                    return <Tag color={color}>{text}</Tag>;
                  }
                }
              ]}
              pagination={false}
              size="small"
            />
          ) : (
            <Empty description="没有元数据差异" />
          )}
        </div>
      </div>
    );
  };
  
  return (
    <div className="resource-version-compare-panel">
      <Card
        title={
          <Space>
            <DiffOutlined />
            <span>版本比较 - {resourceName}</span>
          </Space>
        }
        extra={
          <Button
            type="text"
            icon={<CloseCircleOutlined />}
            onClick={onClose}
          />
        }
        className="compare-card"
      >
        <div className="compare-content">
          <div className="versions-container">
            <div className="version-column">
              {renderVersionInfo(version1, 1)}
            </div>
            <Divider type="vertical" className="version-divider" />
            <div className="version-column">
              {renderVersionInfo(version2, 2)}
            </div>
          </div>
          
          <Divider />
          
          <div className="differences-container">
            <Tabs defaultActiveKey="content">
              <TabPane tab="内容比较" key="content">
                {renderContentDifference()}
              </TabPane>
              <TabPane tab="元数据比较" key="metadata">
                {renderMetadataDifference()}
              </TabPane>
            </Tabs>
          </div>
        </div>
        
        <Divider />
        
        <div className="compare-footer">
          <Space>
            <InfoCircleOutlined />
            <Text type="secondary">
              比较结果显示了两个版本之间的差异。您可以查看内容和元数据的变化，并选择回滚到任一版本。
            </Text>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default ResourceVersionComparePanel;
